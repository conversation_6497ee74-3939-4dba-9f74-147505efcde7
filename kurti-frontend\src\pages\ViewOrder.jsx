import {
  useEmailInvoiceMutation,
  useGenerateInvoiceMutation,
  useOrderInfoQuery,
  useRefundOrderMutation,
  useShiprocketReturnOrderMutation,
  useTrackOrderQuery,
  useUpdateOrderStatusMutation,
  useWhatsappInvoiceMutation,
} from "api/order.api";
import UpiValidationModal from "components/modals/UpiValidationModal";
import { API_ENDPOINTS } from "globals/endpoints";
import { useEffect, useMemo, useState } from "react";
import { Button, Dropdown } from "react-bootstrap";
import { Link, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import useUserStore, { getAuthToken } from "stores/user";
import { getFormattedDate } from "utils";
import Placeholder150 from "../assets/images/Placeholder-150.png";
import "../styles/vieworder.scss";

const ViewOrder = () => {
  const { id } = useParams();
  const { data: orderInfo = {}, refetch: infoRefetch } = useOrderInfoQuery(id);
  const { mutateAsync: updateStatus } = useUpdateOrderStatusMutation();
  const { mutateAsync: returnOrder } = useShiprocketReturnOrderMutation();
  const { mutateAsync: refundOrder } = useRefundOrderMutation();
  const { mutateAsync: generateInvoice } = useGenerateInvoiceMutation();
  const { mutateAsync: emailInvoice } = useEmailInvoiceMutation();
  const { mutateAsync: whatsappInvoice } = useWhatsappInvoiceMutation();
  const [upiModalOpen, setUpiModalOpen] = useState(false);
  const [isInvoiceLoading, setIsInvoiceLoading] = useState(false);
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isWhatsappLoading, setIsWhatsappLoading] = useState(false);
  const [showInvoiceDropdown, setShowInvoiceDropdown] = useState(false);
  const userInfo = useUserStore((state) => state.userInfo);

  const handleCancelOrder = async () => {
    try {
      const result = await updateStatus({
        orderId: orderInfo?._id,
        status: orderInfo?.status == "68" ? "2" : "45",
      });
      if (result?.success) {
        if (orderInfo?.paymentMethod !== "Cash on Delivery") {
          const refundResult = await refundOrder({
            orderId: orderInfo?._id,
          });
          if (refundResult?.success) {
            infoRefetch();
            toast.success("Order cancelled successfully!");
          }
        } else {
          infoRefetch();
          toast.success("Order cancelled successfully!");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const handleRefundOrder = async () => {
    // Check if user has set up UPI ID for COD orders
    if (
      orderInfo?.paymentMethod === "Cash on Delivery" &&
      !userInfo?.user?.upiId
    ) {
      setUpiModalOpen(true);
      return;
    }

    try {
      const returnResponse = await returnOrder({
        order_id: orderInfo?._id,
      });
      if (returnResponse?.success) {
        toast.success("Return request submitted successfully!");
        infoRefetch();
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to submit return request");
    }
  };

  const awb_code = useMemo(() => {
    return orderInfo?.shipments?.[0]?.awb_code;
  }, [orderInfo]);

  const [shipmentStatus, setShipmentStatus] = useState(null);

  const getRefundDate = (deliveredDate) => {
    if (!deliveredDate) {
      console.log("Delivered date is missing or null");
      return null;
    }
    console.log("Delivered date:", deliveredDate);
    const refundDate = new Date(deliveredDate.replace(" ", "T")); // Convert to valid Date format
    refundDate.setHours(refundDate.getHours() + 72);
    console.log("Refund date (delivered + 72h):", refundDate);
    return refundDate;
  };

  // Get delivered date from shipment track activities
  const deliveredActivity = shipmentStatus?.shipment_track_activities?.find(
    (activity) => activity.status === "Delivered"
  );
  const deliveredDate = deliveredActivity?.date;

  const refundDate = getRefundDate(deliveredDate);

  const showRefund =
    shipmentStatus?.shipment_status === 7 && refundDate > new Date();

  const handleDownloadInvoice = async () => {
    try {
      setIsInvoiceLoading(true);
      // First generate the invoice
      const genResponse = await generateInvoice(orderInfo?._id);
      if (genResponse?.success) {
        // Create download URL
        const downloadUrl = `${import.meta.env.VITE_API_URL}${
          API_ENDPOINTS.DOWNLOAD_INVOICE
        }/${orderInfo?._id}/download`;
        const fileName = `invoice-${orderInfo?.orderId || "order"}.pdf`;

        // Get auth token for the request
        const token = getAuthToken();

        try {
          // Method 1: Try fetch with blob (most reliable for modern browsers)
          const response = await fetch(downloadUrl, {
            method: "GET",
            headers: {
              Authorization: token,
              "X-ESHOP-Platform": "ios",
              "X-ESHOP-Version": "1.0.0",
              "Accept-Language": "en",
            },
          });

          if (response.ok) {
            const blob = await response.blob();

            // Force download using blob
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
              // For IE/Edge
              window.navigator.msSaveOrOpenBlob(blob, fileName);
            } else {
              // For other browsers
              const blobUrl = window.URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.href = blobUrl;
              link.download = fileName;
              link.style.display = "none";

              document.body.appendChild(link);
              link.click();

              // Clean up
              setTimeout(() => {
                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);
              }, 100);
            }

            toast.success("Invoice downloaded successfully!");
          } else {
            throw new Error("Fetch failed");
          }
        } catch (fetchError) {
          console.warn(
            "Fetch method failed, trying iframe method:",
            fetchError
          );

          // Method 2: Fallback using hidden iframe (works with auth headers)
          const iframe = document.createElement("iframe");
          iframe.style.display = "none";
          iframe.src = `${downloadUrl}?token=${encodeURIComponent(token)}`;

          document.body.appendChild(iframe);

          // Remove iframe after download starts
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 5000);

          toast.success("Invoice download started!");
        }
      } else {
        toast.error("Failed to generate invoice");
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to download invoice");
    } finally {
      setIsInvoiceLoading(false);
      setShowInvoiceDropdown(false);
    }
  };

  const handleEmailInvoice = async () => {
    try {
      setIsEmailLoading(true);

      // Get auth token for the download link
      const token = getAuthToken();
      console.log(
        "Email: Token retrieved:",
        token ? "Token exists" : "No token found"
      );

      // First generate the invoice to ensure it exists
      const genResponse = await generateInvoice(orderInfo?._id);
      if (genResponse?.success) {
        // Prepare email content
        const downloadUrl = `${import.meta.env.VITE_API_URL}${
          API_ENDPOINTS.DOWNLOAD_INVOICE
        }/${orderInfo?._id}/public-download?token=${encodeURIComponent(token)}`;
        const subject = `Invoice for Order #${orderInfo?.orderId}`;
        const body = `Dear Customer,

Please find your invoice for the recent order below.

Order Details:
- Order ID: ${orderInfo?.orderId}
- Order Date: ${getFormattedDate(orderInfo?.createdAt)}
- Total Amount: ₹${orderInfo?.totalPrice}
- Payment Method: ${orderInfo?.paymentMethod}

Thank you for shopping with us!

Best regards,
Gajan Creation Team`;

        // Create mailto link
        const mailtoLink = `mailto:${
          orderInfo?.shippingAddress?.email || ""
        }?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(
          body
        )}`;

        // Open email client
        window.open(mailtoLink, "_self");
        toast.success("Email client opened with invoice details!");
      } else {
        toast.error("Failed to generate invoice");
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to prepare email");
    } finally {
      setIsEmailLoading(false);
      setShowInvoiceDropdown(false);
    }
  };

  const handleWhatsappInvoice = async () => {
    try {
      setIsWhatsappLoading(true);

      // Get auth token for the download link
      const token = getAuthToken();
      console.log(
        "WhatsApp: Token retrieved:",
        token ? "Token exists" : "No token found"
      );

      // First generate the invoice to ensure it exists
      const genResponse = await generateInvoice(orderInfo?._id);
      if (genResponse?.success) {
        // Prepare WhatsApp message
        const downloadUrl = `${import.meta.env.VITE_API_URL}${
          API_ENDPOINTS.DOWNLOAD_INVOICE
        }/${orderInfo?._id}/public-download?token=${encodeURIComponent(token)}`;
        const message = `🧾 *Invoice for Order #${orderInfo?.orderId}*

📅 *Order Date:* ${getFormattedDate(orderInfo?.createdAt)}
💰 *Total Amount:* ₹${orderInfo?.totalPrice}
💳 *Payment Method:* ${orderInfo?.paymentMethod}
📦 *Status:* ${orderInfo?.statusText}

Thank you for shopping with Gajan Creation! 🛍️`;

        // Detect if user is on mobile device
        const isMobile =
          /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          );

        // Create WhatsApp link (mobile app or web)
        const whatsappLink = isMobile
          ? `whatsapp://send?text=${encodeURIComponent(message)}`
          : `https://web.whatsapp.com/send?text=${encodeURIComponent(message)}`;

        // Open WhatsApp
        window.open(whatsappLink, "_blank");
        toast.success(
          isMobile
            ? "WhatsApp app opened with invoice details!"
            : "WhatsApp Web opened with invoice details!"
        );
      } else {
        toast.error("Failed to generate invoice");
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to prepare WhatsApp message");
    } finally {
      setIsWhatsappLoading(false);
      setShowInvoiceDropdown(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showInvoiceDropdown && !event.target.closest(".dropdown")) {
        setShowInvoiceDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showInvoiceDropdown]);

  useEffect(() => {
    if (shipmentStatus) {
      updateStatus({
        orderId: orderInfo?._id,
        status: shipmentStatus?.shipment_status?.toString(),
      })
        .then(() => {
          infoRefetch();
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [shipmentStatus]);

  return (
    <div className="vieworder-page py-2 py-md-3 px-2 px-md-0">
      <div className="container-fluid px-0 px-md-3">
        <div className="card shadow-sm">
          <div className="card-header bg-primary-title text-white d-flex flex-md-row justify-content-between align-items-center py-2 py-md-3">
            <h5 className="mb-2 mb-md-0 text-light">Order Details</h5>
            <Link
              className="btn btn-outline-light btn-sm"
              to="/dashboard"
              style={{
                whiteSpace: "nowrap",
                padding: "0.25rem 0.75rem",
                fontSize: "0.875rem",
              }}
            >
              Back to My Orders
            </Link>
          </div>
          <div className="card-body p-2 p-md-3">
            <div className="row">
              <div className="col-12 col-lg-6 mb-4 mb-lg-0">
                {orderInfo?.products?.map((product, key) => (
                  <div key={key} className="row g-3 mb-3 p-2 border-bottom">
                    <div className="col-4 col-sm-3">
                      <div className="ratio ratio-1x1">
                        <img
                          src={product?.mainImage || Placeholder150}
                          alt="Order"
                          className="img-fluid rounded"
                          style={{ objectFit: "cover" }}
                        />
                      </div>
                    </div>
                    <div className="col-8 col-sm-9">
                      <h6
                        className="mb-1 text-truncate-2"
                        style={{ fontSize: "0.95rem" }}
                      >
                        {product?.name || ""}
                      </h6>
                      <p className="mb-1 small">
                        <span className="text-muted">Price: </span>
                        <span className="fw-medium">
                          ₹{product?.price || 1}
                        </span>
                      </p>
                      <p className="mb-0 small">
                        <span className="text-muted">Qty: </span>
                        <span>{product?.quantity || 1} piece</span>
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="col-12 col-lg-6">
                <div className="card border-0 shadow-sm">
                  <div className="card-body p-3">
                    <h6 className="mb-3 fw-semibold">Order Summary</h6>
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Status</span>
                      <span className="fw-medium small">
                        {orderInfo?.statusText}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Order ID</span>
                      <span
                        className="fw-medium small text-end text-truncate ms-2"
                        style={{ maxWidth: "60%" }}
                      >
                        {orderInfo?.orderId}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Order Date</span>
                      <span className="fw-medium small">
                        {getFormattedDate(orderInfo?.createdAt)}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Payment Method</span>
                      <span className="fw-medium small text-capitalize">
                        {orderInfo?.paymentMethod}
                      </span>
                    </div>
                    {orderInfo?.paymentInfo?.razorpay_payment_id && (
                      <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <span className="text-muted small">Transaction ID</span>
                        <span
                          className="fw-medium small text-end text-truncate ms-2"
                          style={{ maxWidth: "60%" }}
                        >
                          {orderInfo.paymentInfo.razorpay_payment_id}
                        </span>
                      </div>
                    )}
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Coupon Discount</span>
                      <span className="fw-medium small">
                        -₹{orderInfo?.coupoun?.cDiscount || 0}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span className="text-muted small">Shipping Charges</span>
                      <span className="fw-medium small">
                        ₹{orderInfo?.totalShipping || 0}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center py-3 mt-2">
                      <span className="fw-semibold">Total Amount</span>
                      <span className="fw-bold fs-5">
                        ₹{orderInfo?.totalPrice}
                      </span>
                    </div>
                  </div>
                </div>
                {/* Action Buttons */}
                <div className="d-flex flex-wrap justify-content-end gap-2 mt-3 action-buttons-container">
                  {["68"]?.includes(orderInfo?.status) && (
                    <Button
                      variant="outline-danger"
                      onClick={handleCancelOrder}
                      size="sm"
                      className="px-3"
                    >
                      Cancel Order
                    </Button>
                  )}
                  {showRefund && (
                    <Button
                      variant="primary"
                      onClick={handleRefundOrder}
                      size="sm"
                      className="px-3"
                    >
                      Return Order
                    </Button>
                  )}

                  {/* Invoice Generation Button with Dropdown */}
                  <Button
                    variant="primary"
                    onClick={handleDownloadInvoice}
                    size="sm"
                    className="px-3"
                    style={{
                      backgroundColor: "#6c5ebc",
                      color: "white",
                      border: "1px solid #6c5ebc"
                    }}
                  >
                    Download Invoice
                  </Button>
                  {/* <Dropdown
                    show={showInvoiceDropdown}
                    onToggle={setShowInvoiceDropdown}
                    className="invoice-dropdown"
                  >
                    <Dropdown.Toggle
                      variant=""
                      size="sm"
                      className="px-3"
                      disabled={
                        isInvoiceLoading || isEmailLoading || isWhatsappLoading
                      }
                    >
                      {isInvoiceLoading ||
                      isEmailLoading ||
                      isWhatsappLoading ? (
                        <>
                          <span
                            className="spinner-border spinner-border-sm me-2"
                            role="status"
                            aria-hidden="true"
                          ></span>
                          Processing...
                        </>
                      ) : (
                        <>📄 Download Invoice</>
                      )}
                    </Dropdown.Toggle>

                    <Dropdown.Menu>
                      <Dropdown.Item
                        onClick={handleDownloadInvoice}
                        disabled={isInvoiceLoading}
                      >
                        {isInvoiceLoading ? (
                          <>
                            <span
                              className="spinner-border spinner-border-sm me-2"
                              role="status"
                              aria-hidden="true"
                            ></span>
                            Generating...
                          </>
                        ) : (
                          <>📥 Download PDF</>
                        )}
                      </Dropdown.Item>

                      <Dropdown.Item
                        onClick={handleEmailInvoice}
                        disabled={isEmailLoading}
                      >
                        {isEmailLoading ? (
                          <>
                            <span
                              className="spinner-border spinner-border-sm me-2"
                              role="status"
                              aria-hidden="true"
                            ></span>
                            Preparing...
                          </>
                        ) : (
                          <>📧 Share via Email</>
                        )}
                      </Dropdown.Item>

                      <Dropdown.Item
                        onClick={handleWhatsappInvoice}
                        disabled={isWhatsappLoading}
                      >
                        {isWhatsappLoading ? (
                          <>
                            <span
                              className="spinner-border spinner-border-sm me-2"
                              role="status"
                              aria-hidden="true"
                            ></span>
                            Preparing...
                          </>
                        ) : (
                          <>💬 Share via WhatsApp</>
                        )}
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* UPI Validation Modal */}
      <UpiValidationModal
        open={upiModalOpen}
        onClose={() => setUpiModalOpen(false)}
      />
    </div>
  );
};

export default ViewOrder;

const OrderTimeline = ({ awb_code, setShipmentStatus }) => {
  const { data: { tracking_data } = {} } = useTrackOrderQuery(awb_code);
  const trackingActivities = tracking_data?.shipment_track_activities || [];

  // Reverse the shipment activities array to show statuses in ascending order (oldest first)
  const reversedActivities = [...trackingActivities].reverse();

  useEffect(() => {
    if (tracking_data) {
      setShipmentStatus(tracking_data);
    }
  }, [tracking_data]);

  return (
    <>
      <h6>Order Timeline</h6>
      <ul className="timeline">
        {reversedActivities.map((activity, index) => {
          // Determine if the status has been completed
          const isCompleted = index <= reversedActivities.length - 1;

          return (
            <li
              key={activity.sr - status}
              className={`timeline-item ${isCompleted ? "completed" : ""}`}
            >
              <div className="timeline-marker"></div>
              <div className="timeline-content">
                <h6 className="fw-bold">
                  {activity["sr-status-label"]?.toUpperCase()}
                </h6>
                <p>
                  {isCompleted
                    ? `Status updated on ${new Date(
                        activity.date
                      ).toLocaleDateString()}`
                    : "Pending"}
                </p>
                <p>{activity.activity}</p>
                <p>{activity.location}</p>
              </div>
            </li>
          );
        })}
      </ul>
    </>
  );
};
